import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CyclePickerBottomSheet extends StatefulWidget {
  const CyclePickerBottomSheet({
    super.key,
    required this.type,
    required this.flower,
    required this.onCycleSelected,
  });

  final NurtureType type;
  final Flower? flower;
  final Function(int cycle, bool applyToAllMonths) onCycleSelected;

  @override
  State<CyclePickerBottomSheet> createState() => _CyclePickerBottomSheetState();
}

class _CyclePickerBottomSheetState extends State<CyclePickerBottomSheet> {
  int _selectedCycle = monthlyCycleDataInherit;
  final TextEditingController _daysController = TextEditingController();
  final FocusNode _daysFocusNode = FocusNode();
  String? _errorText;

  @override
  void dispose() {
    _daysController.dispose();
    _daysFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'monthly_cycle_settings.cycle_picker_title'.tr(),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildCycleOption(
              title: 'monthly_cycle_settings.inherit'.tr(),
              subtitle: 'monthly_cycle_settings.inherit_description'.tr(),
              value: monthlyCycleDataInherit,
            ),
            _buildCycleOption(
              title: 'monthly_cycle_settings.unset'.tr(),
              subtitle: 'monthly_cycle_settings.unset_description'.tr(),
              value: monthlyCycleDataUnset,
            ),
            _buildCustomDaysOption(),
            if (_errorText != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorText!,
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
            const SizedBox(height: 30),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _onConfirm(false),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text('monthly_cycle_settings.apply_current_month'.tr()),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _onConfirm(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text('monthly_cycle_settings.apply_all_months'.tr()),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCycleOption({
    required String title,
    required String subtitle,
    required int value,
  }) {
    final isSelected = _selectedCycle == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCycle = value;
          _errorText = null;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomDaysOption() {
    final isCustom = _selectedCycle > 0;

    return GestureDetector(
      onTap: () {
        setState(() {
          if (_selectedCycle <= 0) {
            _selectedCycle = 1;
            _daysController.text = '1';
          }
          _errorText = null;
        });
        // 自动将焦点放到输入框
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _daysFocusNode.requestFocus();
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isCustom ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isCustom ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isCustom ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'monthly_cycle_settings.custom_days'.tr(),
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: isCustom ? Theme.of(context).primaryColor : Colors.black87,
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _daysController,
              focusNode: _daysFocusNode,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                hintText: 'monthly_cycle_settings.input_days_hint'.tr(),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                errorText: _errorText,
              ),
              onChanged: (value) {
                final days = int.tryParse(value);
                if (days != null && days >= 1 && days <= 366) {
                  setState(() {
                    _selectedCycle = days;
                    _errorText = null;
                  });
                } else if (value.isNotEmpty) {
                  setState(() {
                    _errorText = 'monthly_cycle_settings.invalid_days_error'.tr();
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _onConfirm(bool applyToAllMonths) {
    if (_selectedCycle > 0) {
      final days = int.tryParse(_daysController.text);
      if (days == null || days < 1 || days > 366) {
        setState(() {
          _errorText = 'monthly_cycle_settings.invalid_days_error'.tr();
        });
        return;
      }
      _selectedCycle = days;
    }

    widget.onCycleSelected(_selectedCycle, applyToAllMonths);
    Navigator.pop(context);
  }
}
